import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { CalendarModeEnum } from '@shared/enum/calender-mode';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { FundsServiceProxy } from '@core/api/api.generated';
import { UserManagementService } from '@shared/services/users/user-management.service';
import { StrategyService } from '@core/services/strategy.service';
import { FundsService, AddFundCommand } from '../../services/fund.service';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { ErrorModalService } from '@core/services/error-modal.service';
import { BreadcrumbComponent } from '../../../../shared/components/breadcrumb/breadcrumb.component';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { DateConversionService } from '@shared/services/date.service';
import momentHijri from 'moment';
import {
  TokenService,
  userRole,
} from 'src/app/features/auth/services/token.service';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { FundSuccessDialogComponent } from '../fund-success-dialog/fund-success-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-create-fund',
  standalone: true,
  imports: [
    CommonModule,
    FormBuilderComponent,
    PageHeaderComponent,
    TranslateModule,
    RouterModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CustomButtonComponent,
    BreadcrumbComponent,
  ],
  providers: [DateConversionService],
  templateUrl: './create-fund.component.html',
  styleUrls: ['./create-fund.component.scss'],
})
export class CreateFundComponent implements OnInit {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [
    {
      label: 'INVESTMENT_FUNDS.TITLE',
      url: '/admin/investment-funds',
      icon: 'fas fa-home',
    },
    {
      label: 'INVESTMENT_FUNDS.CREATE_NEW_FUND',
      url: '/admin/investment-funds/create',
      disabled: true,
    },
  ];

  formGroup!: FormGroup;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  isFormSubmitted: boolean | undefined = false;
  isValidationFire: boolean | undefined = false;
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'name',
      id: 'name',
      name: 'name',
      label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
      placeholder: 'INVESTMENT_FUNDS.FORM.FUND_NAME_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'strategyId',
      id: 'strategyId',
      name: 'strategyId',
      label: 'INVESTMENT_FUNDS.FORM.STRATEGY',
      placeholder: 'INVESTMENT_FUNDS.FORM.STRATEGY_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      options: [],
    },
    {
      type: InputType.Number,
      formControlName: 'propertiesNumber',
      id: 'propertiesNumber',
      name: 'propertiesNumber',
      label: 'INVESTMENT_FUNDS.FORM.PROPERTY_COUNT',
      placeholder: 'INVESTMENT_FUNDS.FORM.PROPERTY_COUNT_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      max: 50,
      min: 1,
      step: 1,
    },
    {
      type: InputType.Date,
      formControlName: 'initiationDate',
      id: 'initiationDate',
      name: 'initiationDate',
      label: 'INVESTMENT_FUNDS.FORM.CREATION_DATE',
      placeholder: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Radio,
      formControlName: 'votingTypeId',
      id: 'votingTypeId',
      name: 'votingTypeId',
      label: 'INVESTMENT_FUNDS.FORM.VOTING_METHOD',
      isRequired: true,
      class: 'col-md-5 col-lg-4',
      options: [
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_ALL', id: 1 },
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_MEMBERS', id: 2 },
      ],
    },
    {
      type: InputType.empty,
      formControlName: '',
      id: '',
      name: '',
      label: '',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'fundManagers',
      id: 'fundManagers',
      name: 'fundManagers',
      label: 'INVESTMENT_FUNDS.FORM.FUND_MANAGER',
      placeholder: 'INVESTMENT_FUNDS.FORM.SELECT_OFFICIALS',
      isRequired: true,
      maxLength: 3,
      class: 'col-md-4',
      multiple: true,
      options: [],
    },
    {
      type: InputType.Dropdown,
      formControlName: 'fundBoardSecretaries',
      id: 'fundBoardSecretaries',
      name: 'fundBoardSecretaries',
      label: 'INVESTMENT_FUNDS.FORM.SECRETARY',
      placeholder: 'INVESTMENT_FUNDS.FORM.SELECT_OFFICIALS',
      isRequired: false,
      class: 'col-md-4',
      maxLength: 4,
      multiple: true,
      options: [],
    },
    {
      type: InputType.Dropdown,
      formControlName: 'legalCouncilId',
      id: 'legalCouncilId',
      name: 'legalCouncilId',
      label: 'INVESTMENT_FUNDS.FORM.LEGAL_ADVISOR',
      placeholder: 'INVESTMENT_FUNDS.FORM.LEGAL_ADVISOR',
      isRequired: true,
      class: 'col-md-4',
      isReadonly: true,
      maxLength: 1,
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'INVESTMENT_FUNDS.FORM.TERMS_FILE',
      placeholder: 'INVESTMENT_FUNDS.FORM.DRAG_DROP_FILES',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['pdf'],
      moduleId: AttachmentModule.Fund,
    },
  ];
  isLegalCouncil: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    public router: Router,
    private userService: UserManagementService,
    private strategyService: StrategyService,
    private fundService: FundsService,
    private errorModalService: ErrorModalService,
    private DateConversionService: DateConversionService,
    private tokenService: TokenService,
    private dialog: MatDialog
  ) {}
  maxGreg?: NgbDateStruct;
  maxHijri?: NgbDateStruct;
  integerValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value === null || value === '') return null;
      return Number.isInteger(+value) ? null : { integer: true };
    };
  }
  setDateMaxAndMin() {
    const today = new Date();
    const minGreg: NgbDateStruct = { year: 2010, month: 1, day: 1 };
    const minHijri: NgbDateStruct =
      this.DateConversionService.convertGregorianToHijri(minGreg);

    this.maxGreg = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    this.maxHijri = this.DateConversionService.convertGregorianToHijri(
      this.maxGreg
    );
    this.initForm();
    const field = this.formControls.find(
      (f) => f.formControlName === 'initiationDate'
    );
    if (field) {
      field.maxGreg = this.maxGreg;
      field.maxHijri = this.maxHijri;
      field.minHijri = minHijri;
      field.minGreg = minGreg;
    }
  }
  ngOnInit() {
    this.updateFormControlsArray();
    this.setDateMaxAndMin();
    this.getStrategyList();
    this.getFundManagerUsers();
    this.getBoardSecretaryUsers();
    this.getLegalCouncilUsers();
  }
  updateFormControlsArray() {
    this.isLegalCouncil = this.tokenService.hasRole(userRole.legalCouncil);
    if (this.isLegalCouncil) {
      const firstControl: IControlOption = {
        type: InputType.Mixed,
        formControlName: 'oldCode',
        id: 'oldCode',
        name: 'oldCode',
        label: 'INVESTMENT_FUNDS.FORM.OLD_CODE',
        placeholder: 'INVESTMENT_FUNDS.FORM.OLD_CODE_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-4',
        maxLength: 20,
      };

      const secondControl: IControlOption = {
        type: InputType.empty,
        label: '',
        name: '',
        class: 'col-md-8',
        formControlName: '',
        id: '',
      };
      const thirdControl: IControlOption = {
        type: InputType.Date,
        formControlName: 'exitDate',
        id: 'exitDate',
        name: 'exitDate',
        label: 'INVESTMENT_FUNDS.FORM.EXIT_DATE',
        placeholder: 'INVESTMENT_FUNDS.FORM.EXIT_DATE_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-4',
      };
      this.formControls.splice(0, 0, firstControl);
      this.formControls.splice(1, 0, secondControl);
      this.formControls.splice(6, 0, thirdControl);
    } else {
      const fieldsToUpdate = ['propertiesNumber', 'strategyId'];

      fieldsToUpdate.forEach((controlName) => {
        const field = this.formControls.find(
          (f) => f.formControlName === controlName
        );
        if (field) {
          field.isRequired = false;
        }
      });
    }
  }
  getStrategyList() {
    this.strategyService.strategyList(0, 0, '', '').subscribe((res: any) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'strategyId'
      );
      if (field) {
        field.options = res.data.map((user: any) => ({
          id: user.id,
          name: user.localizedName,
        }));
      }
    });
  }
  getFundManagerUsers() {
    this.userService.getFundManagerUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'fundManagers'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
      }
      if (!this.isLegalCouncil)
        this.formGroup
          .get('fundManagers')
          ?.setValue([Number(this.tokenService.getuserId())]);
    });
  }
  getBoardSecretaryUsers() {
    this.userService.getBoardSecretaryUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'fundBoardSecretaries'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
      }
    });
  }
  getLegalCouncilUsers() {
    this.userService.getLegalCouncilUsers().subscribe((managers) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'legalCouncilId'
      );
      if (field) {
        field.options = managers.data.map((user) => ({
          id: user.id,
          name: user.fullName,
        }));
        this.formGroup.get('legalCouncilId')?.setValue(field.options[0].id);
        this.formGroup.get('legalCouncilId')?.disable();
      }
    });
  }

  private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      if (control.type == InputType.Number) {
        validators.push(this.integerValidator());
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
    this.formGroup.get('votingTypeId')?.setValue(2);
  }
  onFileUpload(data: any) {
    this.formGroup.get(data.control.formControlName)?.setValue(data.file.id);
  }
  onBreadcrumbClicked(breadcrumbItem: IBreadcrumbItem) {
    console.log('breadcrumbClicked', breadcrumbItem);
  }

  dateSelected(event: { event: any; control: IControlOption }) {
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(event.event.formattedGregorian);
  }
  onSubmit(formValue: any) {
    this.isValidationFire = true;

    if (this.formGroup.valid && !this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callApi();
    }
  }
  onRemoveSelectedItem(data: any) {
    if (
      data.control.formControlName == 'fundManagers' &&
      data.idToRemove == Number(this.tokenService.getuserId())
    )
      return;
    const current =
      this.formGroup.get(data.control.formControlName)?.value || [];
    const updated = current.filter((id: any) => id !== data.idToRemove);
    this.formGroup.get(data.control.formControlName)?.setValue(updated);
  }
  setData(): AddFundCommand {
    return {
      id: 0,
      name: this.formGroup.get('name')?.value ?? '',
      strategyId: this.formGroup.get('strategyId')?.value ?? 0,
      strategyName: this.formGroup.get('strategyName')?.value ?? '',
      status: this.formGroup.get('status')?.value ?? '',
      statusId: this.formGroup.get('statusId')?.value ?? 0,
      initiationDate: this.formGroup.get('initiationDate')?.value ?? '',
      exitDate: this.formGroup.get('exitDate')?.value ?? null,
      oldCode: this.formGroup.get('oldCode')?.value ?? '',
      propertiesNumber:
        Number(this.formGroup.get('propertiesNumber')?.value) ?? 0,
      attachmentId: this.formGroup.get('attachmentId')?.value ?? 0,
      votingTypeId: this.formGroup.get('votingTypeId')?.value ?? 0,
      legalCouncilId: this.formGroup.get('legalCouncilId')?.value ?? 0,
      fundManagers: this.formGroup.get('fundManagers')?.value ?? [],
      fundBoardSecretaries:
        this.formGroup.get('fundBoardSecretaries')?.value ?? [],
    };
  }
  callApi() {
    this.formGroup.get('legalCouncilId')?.enable();
    const body: AddFundCommand = this.setData();
    this.fundService.addFund(body).subscribe({
      next: (response: any) => {
     this.dialog.closeAll();
    const dialogRef = this.dialog.open(FundSuccessDialogComponent, {
      width: '500px',
    });
        this.router.navigate(['/admin/investment-funds']);
      },
      error: (error: any) => {
        this.isFormSubmitted = false;
        this.isValidationFire = false;
        console.log('error', error);
      },
    });
  }

  dropdownChanged(data: any) {
    debugger;
    if (
      data.control.formControlName == 'fundManagers' &&
      !this.isLegalCouncil
    ) {
      if (data.event.length == 0)
        this.formGroup
          .get('fundManagers')
          ?.setValue([Number(this.tokenService.getuserId())]);
      else
      {
         const current =
      this.formGroup.get(data.control.formControlName)?.value || [];
      const updated = current.filter((id: any) => id !== Number(this.tokenService.getuserId()));
      this.formGroup.get(data.control.formControlName)?.setValue([...updated,Number(this.tokenService.getuserId())]);
      }
    }
  }
}
