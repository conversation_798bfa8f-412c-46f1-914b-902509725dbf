import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { FundsServiceProxy } from '@core/api/api.generated';
import { TokenService } from 'src/app/features/auth/services/token.service';

@Injectable({
  providedIn: 'root'
})
export class FundAccessGuard implements CanActivate {

  constructor(
    private fundsService: FundsServiceProxy,
    private tokenService: TokenService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    if (!this.tokenService.isLoggedIn()) {
      this.router.navigate(['/auth/login']);
      return false;
    }

    const fundId = this.extractFundId(route);
    if (!fundId) {
      this.router.navigate(['/admin/investment-funds']);
      return false;
    }

    // Check user access to the specific fund
    return this.fundsService.checkUserAccess(fundId).pipe(
      map(response => {
        if (response.successed && response.data && response.data.hasAccess) {
          return true;
        } else {
          this.router.navigate(['/admin/investment-funds']);
          return false;
        }
      }),
      catchError(error => {
        this.router.navigate(['/admin/investment-funds']);
        return of(false);
      })
    );
  }

  private extractFundId(route: ActivatedRouteSnapshot): number | null {
    const id = route.queryParams['id'];
    if (id) {
      const parsedId = parseInt(id, 10);
      return isNaN(parsedId) ? null : parsedId;
    }

    const fundId = route.queryParams['fundId'];
    if (fundId) {
      const parsedFundId = parseInt(fundId, 10);
      return isNaN(parsedFundId) ? null : parsedFundId;
    }

    return null;
  }
}
