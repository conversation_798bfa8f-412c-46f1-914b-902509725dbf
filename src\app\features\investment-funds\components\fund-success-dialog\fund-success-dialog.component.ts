import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { AddMemberComponent } from 'src/app/features/members/add-member/add-member.component';

@Component({
  selector: 'app-fund-success-dialog',
  standalone: true,
  imports: [CommonModule,
    TranslateModule, CustomButtonComponent],
  templateUrl: './fund-success-dialog.component.html',
  styleUrl: './fund-success-dialog.component.scss'
})
export class FundSuccessDialogComponent {
   buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;
  constructor(
    public dialogRef: MatDialogRef<FundSuccessDialogComponent>,public router:Router,  private cdr: ChangeDetectorRef
  ) {

  }
  onSubmit(){
    this.dialogRef.close(true);
    this.router.navigate(['/admin/investment-funds/members']);

  }

  onClose(): void {
    this.dialogRef.close(false);
      this.cdr.detectChanges();
  }
}
