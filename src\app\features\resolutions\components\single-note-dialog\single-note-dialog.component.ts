import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from "@shared/components/custom-button/custom-button.component";
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

@Component({
  selector: 'app-single-note-dialog',
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule, CustomButtonComponent],
  templateUrl: './single-note-dialog.component.html',
  styleUrl: './single-note-dialog.component.scss'
})
export class SingleNoteDialogComponent {
  body ='';
    buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;
 constructor(
    public dialogRef: MatDialogRef<SingleNoteDialogComponent>,
    private translateService: TranslateService,@Inject(MAT_DIALOG_DATA) public data: any
  ) {

  }

  ngOnInit(): void {


  }

  onCancel(): void {
    this.dialogRef.close();
  }
  addNote(){}
}
