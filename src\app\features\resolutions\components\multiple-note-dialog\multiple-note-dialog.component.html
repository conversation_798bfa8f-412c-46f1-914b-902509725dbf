<div class="dialog-container">
  <h2 class=" header">
    {{ data.isResolution ? ('INVESTMENT_FUNDS.VOTING.DECISION_COMMENTS' | translate) :
    ('INVESTMENT_FUNDS.VOTING.ITEM_COMMENTS' | translate) }}
  </h2>
<hr>

  <div class="form-fields">
    <div class="member-note-wrapper">
  <div class="row member-note-header">
    <div class="col-md-1 img-container">
      <img src="assets/images/avatar-member.png" alt="Member Avatar"
        class="avatar-img">
    </div>
    <div class="col-md-11 p-0">

      <div class="member-note-user-info">
        <span class="user-name">عبدالرحمن أحمد</span>
      </div>
      <div class="member-note-role d-flex justify-content-between">
        <span class="user-role"> مستقل</span>
        <p class="user-time">منذ 15 ساعة</p>
      </div>

      <div class="member-note-body">
        <p>ملاحظات التصويت ملاحظات التصويت ملاحظات التصويت ملاحظات التصويت\nملاحظات التصويت ملاحظات التصويت ملاحظات التصويت</p>
      </div>
    </div>
  </div>

</div>
    <!-- <div class="member-note-container">

      <app-member-note
        [userName]="'محمد أحمد عبدالرحمن أحمد'"
        [userRole]="'مستقل'"
        [timeAgo]="'منذ 15 ساعة'"
        [commentText]="''">
      </app-member-note>
          </div> -->

      <hr>
    <div class="form-container">
      <textarea
        class="form-control form-control-solid"
        type="text"
        [(ngModel)]="body"
        name="body"
        #note="ngModel"
        placeholder="{{'INVESTMENT_FUNDS.VOTING.VOTING_COMMENTS' | translate}}"
        required
        ></textarea>
      <div *ngIf="note.invalid && note.touched"
        class="text-danger">
        <div *ngIf="note.errors?.['required']">{{'FORM.ERROR_REQUIRED' | translate}}</div>
      </div>
    </div>
  </div>
<hr>
  <div class="dialog-actions">
      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="onCancel()" [buttonType]="buttonEnum.Secondary"
        [iconName]="IconEnum.cancel">
      </app-custom-button>

    <app-custom-button [btnName]="'INVESTMENT_FUNDS.VOTING.ADD_COMMENT' | translate" (click)="addNote()" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify">
      </app-custom-button>
  </div>
</div>


