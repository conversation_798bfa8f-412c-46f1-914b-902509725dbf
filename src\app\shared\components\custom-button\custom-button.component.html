<button type="button" class="w-100 font-size-m" [ngClass]="{
    'btn': true,
    'primary-btn': buttonType === ButtonTypeEnum.Primary,
    'secondary-btn': buttonType === ButtonTypeEnum.Secondary,
    'danger-btn': buttonType === ButtonTypeEnum.Danger,
    'outline-btn': buttonType === ButtonTypeEnum.OutLine,
    'success-btn': buttonType === ButtonTypeEnum.Success
  }"  [class]="class" (click)="action($event)"
  [ngStyle]="{cursor:disabled?'not-allowed':'pointer'}"
  [disabled]="disabled"
  [title]="btnName"
>
  <img *ngIf="iconName"  [src]="iconName" class="mx-1 {{iconName ==IconEnum.arrowNavyRight?'rotate-icon':''}}"

  alt="icon" />
  {{ btnName}}
</button>
